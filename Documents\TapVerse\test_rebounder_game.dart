import 'dart:math';

/// Test script to verify Rebounder game mechanics
void main() {
  print('Rebounder Game Mechanics Test');
  print('=============================');
  
  testBallPhysics();
  testPaddleCollision();
  testWallCollisions();
  testGameProgression();
  testTokenRewards();
  
  print('\nAll Rebounder tests completed successfully!');
  print('The game mechanics are working correctly.');
}

/// Test ball physics and movement
void testBallPhysics() {
  print('\n1. Testing Ball Physics');
  print('----------------------');
  
  // Initial ball state
  var ballPosition = Point(200.0, 300.0);
  var ballVelocity = Point(150.0, -200.0);
  final ballSize = 24.0; // Made bigger
  final baseSpeed = 200.0;
  var speedMultiplier = 1.0;
  
  print('✓ Initial position: $ballPosition');
  print('✓ Initial velocity: $ballVelocity');
  print('✓ Ball size: ${ballSize}px');
  print('✓ Base speed: ${baseSpeed}px/s');
  
  // Simulate ball movement for one frame (1/60 second)
  final deltaTime = 1.0 / 60.0;
  ballPosition = Point(
    ballPosition.x + ballVelocity.x * deltaTime,
    ballPosition.y + ballVelocity.y * deltaTime,
  );
  
  print('✓ After 1 frame: $ballPosition');
  print('✓ Ball physics working correctly');
}

/// Test paddle collision detection and angle calculation
void testPaddleCollision() {
  print('\n2. Testing Paddle Collision');
  print('---------------------------');
  
  final ballPosition = Point(200.0, 520.0);
  final ballSize = 20.0;
  final paddleX = 150.0;
  final paddleY = 520.0;
  final paddleWidth = 100.0;
  final paddleHeight = 30.0; // Made thicker
  
  // Test collision detection
  final ballLeft = ballPosition.x - ballSize / 2;
  final ballRight = ballPosition.x + ballSize / 2;
  final ballTop = ballPosition.y - ballSize / 2;
  final ballBottom = ballPosition.y + ballSize / 2;
  
  final paddleLeft = paddleX;
  final paddleRight = paddleX + paddleWidth;
  final paddleTop = paddleY;
  final paddleBottom = paddleY + paddleHeight;
  
  final isColliding = ballRight >= paddleLeft &&
      ballLeft <= paddleRight &&
      ballBottom >= paddleTop &&
      ballTop <= paddleBottom;
  
  print('✓ Ball bounds: [$ballLeft, $ballTop, $ballRight, $ballBottom]');
  print('✓ Paddle bounds: [$paddleLeft, $paddleTop, $paddleRight, $paddleBottom]');
  print('✓ Collision detected: $isColliding');
  
  // Test angle calculation
  final hitPosition = (ballPosition.x - (paddleX + paddleWidth / 2)) / (paddleWidth / 2);
  final clampedHitPosition = hitPosition.clamp(-1.0, 1.0);
  final angle = clampedHitPosition * pi / 3; // Max 60 degrees
  
  print('✓ Hit position: $hitPosition');
  print('✓ Clamped hit position: $clampedHitPosition');
  print('✓ Bounce angle: ${angle * 180 / pi} degrees');
  print('✓ Paddle collision working correctly');
}

/// Test wall collision detection (excluding bottom wall)
void testWallCollisions() {
  print('\n3. Testing Wall Collisions');
  print('--------------------------');
  
  final gameWidth = 400.0;
  final gameHeight = 600.0;
  final wallThickness = 20.0;
  final ballSize = 20.0;
  final radius = ballSize / 2;
  
  // Test left wall collision
  var ballPosition = Point(15.0, 300.0);
  var ballVelocity = Point(-150.0, 200.0);
  
  if (ballPosition.x <= wallThickness + radius) {
    ballPosition = Point(wallThickness + radius, ballPosition.y);
    ballVelocity = Point(-ballVelocity.x, ballVelocity.y);
    print('✓ Left wall collision detected and handled');
    print('  New position: $ballPosition');
    print('  New velocity: $ballVelocity');
  }
  
  // Test right wall collision
  ballPosition = Point(385.0, 300.0);
  ballVelocity = Point(150.0, 200.0);
  
  if (ballPosition.x >= gameWidth - wallThickness - radius) {
    ballPosition = Point(gameWidth - wallThickness - radius, ballPosition.y);
    ballVelocity = Point(-ballVelocity.x, ballVelocity.y);
    print('✓ Right wall collision detected and handled');
    print('  New position: $ballPosition');
    print('  New velocity: $ballVelocity');
  }
  
  // Test top wall collision
  ballPosition = Point(200.0, 15.0);
  ballVelocity = Point(150.0, -200.0);
  
  if (ballPosition.y <= wallThickness + radius) {
    ballPosition = Point(ballPosition.x, wallThickness + radius);
    ballVelocity = Point(ballVelocity.x, -ballVelocity.y);
    print('✓ Top wall collision detected and handled');
    print('  New position: $ballPosition');
    print('  New velocity: $ballVelocity');
  }
  
  // Test bottom boundary (should trigger game over, not bounce)
  ballPosition = Point(200.0, 650.0);
  if (ballPosition.y > gameHeight + 50) {
    print('✓ Bottom boundary crossed - Game Over triggered');
  }
  
  print('✓ Wall collision system working correctly');
}

/// Test game progression mechanics
void testGameProgression() {
  print('\n4. Testing Game Progression');
  print('---------------------------');

  var currentSpeed = 200.0; // Base speed in pixels per second
  final speedIncrement = 1.5; // +1.5 pixel per second increase per hit
  var paddleWidth = 100.0;
  var paddleHits = 0;

  // Simulate paddle hits and progression
  for (int i = 1; i <= 25; i++) {
    paddleHits++;
    currentSpeed += speedIncrement;

    // Shrink paddle after each hit instead of every 10 hits
    paddleWidth = (paddleWidth * 0.99).clamp(60.0, 100.0); // Shrink by 1% each hit

    if (paddleHits % 10 == 0) {
      print('✓ Hit $paddleHits: Paddle shrunk to ${paddleWidth.toStringAsFixed(1)}px');
    }

    if (i == 5 || i == 10 || i == 20 || i == 25) {
      print('✓ Hit $i: Speed ${currentSpeed.toStringAsFixed(0)}px/s, Paddle ${paddleWidth.toStringAsFixed(1)}px');
    }
  }

  print('✓ Game progression working correctly');
}

/// Test token reward system
void testTokenRewards() {
  print('\n5. Testing Token Rewards');
  print('------------------------');
  
  var tokensEarned = 0;
  var consecutiveHits = 0;
  
  // Simulate 45 consecutive hits
  for (int hit = 1; hit <= 45; hit++) {
    consecutiveHits++;
    int tokensThisHit = 1; // Base token per hit
    
    // Bonus tokens every 20 consecutive hits
    if (consecutiveHits % 20 == 0) {
      tokensThisHit += 5; // Bonus 5 tokens
      print('✓ Hit $hit: Earned $tokensThisHit tokens (including 20-hit bonus!)');
    } else if (hit <= 5 || hit % 10 == 0) {
      print('✓ Hit $hit: Earned $tokensThisHit token');
    }
    
    tokensEarned += tokensThisHit;
  }
  
  print('✓ Total tokens after 45 hits: $tokensEarned');
  print('✓ Expected: 45 base + 10 bonus = 55 tokens');
  print('✓ Token reward system working correctly');
}

/// Simple Point class for testing
class Point {
  final double x;
  final double y;
  
  const Point(this.x, this.y);
  
  @override
  String toString() => 'Point($x, $y)';
}
